<template>
  <div class="documento-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Subir' }} Documento</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica la información del documento' : 'Sube un nuevo documento al sistema' }}</p>
        </div>
      </div>

      <form @submit.prevent="handleSubmit">
        <div class="row">
          <!-- Información del documento -->
          <div class="col-lg-8">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-file-alt me-2"></i>Información del Documento
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-8">
                    <label class="form-label">Título del documento *</label>
                    <input
                      v-model="form.titulo"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.titulo }"
                      placeholder="Ej: Contrato de alquiler - Juan Pérez"
                      required
                    >
                    <div v-if="errors.titulo" class="invalid-feedback">
                      {{ errors.titulo }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Tipo de documento *</label>
                    <select
                      v-model="form.tipo"
                      class="form-select"
                      :class="{ 'is-invalid': errors.tipo }"
                      required
                    >
                      <option value="">Selecciona...</option>
                      <option value="contrato">Contrato de alquiler</option>
                      <option value="factura">Factura</option>
                      <option value="recibo">Recibo de pago</option>
                      <option value="identificacion">Documento de identidad</option>
                      <option value="nomina">Nómina</option>
                      <option value="aval">Aval bancario</option>
                      <option value="seguro">Seguro</option>
                      <option value="inventario">Inventario</option>
                      <option value="incidencia">Reporte de incidencia</option>
                      <option value="otro">Otro</option>
                    </select>
                    <div v-if="errors.tipo" class="invalid-feedback">
                      {{ errors.tipo }}
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Descripción</label>
                    <textarea
                      v-model="form.descripcion"
                      class="form-control"
                      :class="{ 'is-invalid': errors.descripcion }"
                      rows="3"
                      placeholder="Describe el contenido del documento, fecha, observaciones relevantes..."
                    ></textarea>
                    <div v-if="errors.descripcion" class="invalid-feedback">
                      {{ errors.descripcion }}
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Fecha del documento</label>
                    <input
                      v-model="form.fecha_documento"
                      type="date"
                      class="form-control"
                      :class="{ 'is-invalid': errors.fecha_documento }"
                      :max="today"
                    >
                    <small class="form-text text-muted">
                      Fecha en la que se creó o firmó el documento
                    </small>
                    <div v-if="errors.fecha_documento" class="invalid-feedback">
                      {{ errors.fecha_documento }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Fecha de vencimiento</label>
                    <input
                      v-model="form.fecha_vencimiento"
                      type="date"
                      class="form-control"
                      :class="{ 'is-invalid': errors.fecha_vencimiento }"
                      :min="today"
                    >
                    <small class="form-text text-muted">
                      Fecha de vencimiento (si aplica)
                    </small>
                    <div v-if="errors.fecha_vencimiento" class="invalid-feedback">
                      {{ errors.fecha_vencimiento }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Asociación con entidades -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-link me-2"></i>Asociación
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Relacionado con</label>
                    <select
                      v-model="form.tipoRelacion"
                      class="form-select"
                      :class="{ 'is-invalid': errors.tipoRelacion }"
                      @change="handleTipoRelacionChange"
                    >
                      <option value="">Sin asociación específica</option>
                      <option value="propiedad">Propiedad (Trastero/Piso)</option>
                      <option value="cliente">Cliente</option>
                      <option value="alquiler">Contrato de alquiler</option>
                      <option value="edificio">Edificio/General</option>
                    </select>
                    <div v-if="errors.tipoRelacion" class="invalid-feedback">
                      {{ errors.tipoRelacion }}
                    </div>
                  </div>

                  <!-- Selector de propiedad -->
                  <div v-if="form.tipoRelacion === 'propiedad'" class="col-md-6">
                    <PropiedadSelector
                      v-model="form.propiedadData"
                      label="Seleccionar propiedad"
                      :error="errors.propiedad_id"
                      :filter-by-status="null"
                      :show-create-button="false"
                      help-text="Propiedad asociada al documento"
                      @change="handlePropiedadChange"
                    />
                  </div>

                  <!-- Selector de cliente -->
                  <div v-if="form.tipoRelacion === 'cliente'" class="col-md-6">
                    <ClienteSelector
                      v-model="form.client_id"
                      label="Seleccionar cliente"
                      :error="errors.client_id"
                      :filter-by-status="null"
                      :show-create-button="false"
                      help-text="Cliente asociado al documento"
                      @change="handleClienteChange"
                    />
                  </div>

                  <!-- Selector de alquiler -->
                  <div v-if="form.tipoRelacion === 'alquiler'" class="col-md-6">
                    <label class="form-label">Contrato de alquiler</label>
                    <select
                      v-model="form.alquiler_id"
                      class="form-select"
                      :class="{ 'is-invalid': errors.alquiler_id }"
                      :disabled="isLoadingAlquileres"
                    >
                      <option value="">Selecciona un contrato...</option>
                      <option
                        v-for="alquiler in alquileres"
                        :key="alquiler.id"
                        :value="alquiler.id"
                      >
                        {{ formatAlquilerOption(alquiler) }}
                      </option>
                    </select>
                    <div v-if="errors.alquiler_id" class="invalid-feedback">
                      {{ errors.alquiler_id }}
                    </div>
                  </div>
                </div>

                <!-- Información de la entidad seleccionada -->
                <div v-if="selectedEntityInfo" class="alert alert-info py-2">
                  <small>
                    <strong>Asociado con:</strong> {{ selectedEntityInfo }}
                  </small>
                </div>
              </div>
            </div>
          </div>

          <!-- Archivo y preview -->
          <div class="col-lg-4">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-file-upload me-2"></i>Archivo
                </h5>
              </div>
              <div class="card-body">
                <!-- Zona de drag & drop -->
                <div
                  class="file-drop-zone"
                  :class="{
                    'drag-over': isDragOver,
                    'has-file': form.archivo || existingFile
                  }"
                  @dragover.prevent="isDragOver = true"
                  @dragleave.prevent="isDragOver = false"
                  @drop.prevent="handleFileDrop"
                  @click="$refs.fileInput.click()"
                >
                  <input
                    ref="fileInput"
                    type="file"
                    class="d-none"
                    :accept="acceptedFileTypes"
                    @change="handleFileChange"
                  >

                  <div v-if="!form.archivo && !existingFile" class="text-center py-4">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <p class="mb-2">
                      <strong>Arrastra un archivo aquí</strong><br>
                      o haz clic para seleccionar
                    </p>
                    <small class="text-muted">
                      Formatos: {{ ALLOWED_FILE_TYPES.join(', ').toUpperCase() }}<br>
                      Tamaño máximo: {{ formatFileSize(MAX_FILE_SIZE) }}
                    </small>
                  </div>

                  <!-- Preview del archivo -->
                  <div v-else class="file-preview">
                    <div class="d-flex align-items-center mb-2">
                      <i :class="getFileIcon()" class="fa-2x me-3"></i>
                      <div class="flex-grow-1">
                        <div class="fw-bold">{{ getFileName() }}</div>
                        <small class="text-muted">{{ getFileSize() }}</small>
                      </div>
                      <button
                        type="button"
                        class="btn btn-sm btn-outline-danger"
                        @click.stop="removeFile"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                    </div>

                    <!-- Preview de imagen -->
                    <div v-if="isImageFile() && filePreviewUrl" class="text-center">
                      <img
                        :src="filePreviewUrl"
                        alt="Preview"
                        class="img-fluid rounded"
                        style="max-height: 200px;"
                      >
                    </div>

                    <!-- Información adicional del archivo -->
                    <div class="mt-2">
                      <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        {{ getFileTypeDescription() }}
                      </small>
                    </div>
                  </div>
                </div>

                <div v-if="errors.archivo" class="invalid-feedback d-block mt-2">
                  {{ errors.archivo }}
                </div>

                <!-- Progreso de subida -->
                <div v-if="uploadProgress > 0 && uploadProgress < 100" class="mt-3">
                  <div class="d-flex justify-content-between mb-1">
                    <small>Subiendo archivo...</small>
                    <small>{{ uploadProgress }}%</small>
                  </div>
                  <div class="progress">
                    <div
                      class="progress-bar"
                      :style="{ width: uploadProgress + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Configuración adicional -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-cog me-2"></i>Configuración
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Estado del documento</label>
                  <select
                    v-model="form.estado"
                    class="form-select"
                    :class="{ 'is-invalid': errors.estado }"
                  >
                    <option value="pendiente">Pendiente de revisión</option>
                    <option value="procesado">Procesado</option>
                    <option value="archivado">Archivado</option>
                    <option value="rechazado">Rechazado</option>
                  </select>
                  <div v-if="errors.estado" class="invalid-feedback">
                    {{ errors.estado }}
                  </div>
                </div>

                <div class="form-check">
                  <input
                    v-model="form.es_confidencial"
                    class="form-check-input"
                    type="checkbox"
                    id="confidencial"
                  >
                  <label class="form-check-label" for="confidencial">
                    Documento confidencial
                  </label>
                  <small class="form-text text-muted d-block">
                    Restringe el acceso al documento
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Botones de acción -->
        <div class="row">
          <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
              <router-link to="/documentos" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>Cancelar
              </router-link>
              <button
                type="button"
                class="btn btn-outline-primary"
                @click="saveDraft"
                :disabled="isSubmitting || !form.titulo"
              >
                <i class="fas fa-save me-1"></i>Guardar borrador
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="isSubmitting || !isFormValid"
              >
                <i class="fas fa-upload me-1"></i>
                {{ isSubmitting ? 'Subiendo...' : (isEdit ? 'Actualizar' : 'Subir documento') }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from 'vue-toastification'
import { documentosService } from '@/services/documentos'
import { alquileresService } from '@/services/alquileres'
import { clientesService } from '@/services/clientes'
import { TIPOS_PROPIEDAD, MAX_FILE_SIZE, ALLOWED_FILE_TYPES } from '@/utils/constants'
import ClienteSelector from '@/components/common/ClienteSelector.vue'
import PropiedadSelector from '@/components/common/PropiedadSelector.vue'
import moment from 'moment'

export default {
  name: 'DocumentoForm',
  components: {
    ClienteSelector,
    PropiedadSelector
  },
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const toast = useToast()

    const isSubmitting = ref(false)
    const isDragOver = ref(false)
    const uploadProgress = ref(0)
    const errors = ref({})
    const alquileres = ref([])
    const isLoadingAlquileres = ref(false)
    const filePreviewUrl = ref(null)
    const existingFile = ref(null)

    const form = ref({
      titulo: '',
      tipo: '',
      descripcion: '',
      fecha_documento: null,
      fecha_vencimiento: null,
      tipoRelacion: '',
      propiedadData: { tipo: '', id: null },
      client_id: null,
      alquiler_id: null,
      estado: 'pendiente',
      es_confidencial: false,
      archivo: null
    })

    const today = computed(() => moment().format('YYYY-MM-DD'))
    const isEdit = computed(() => !!props.id)

    const acceptedFileTypes = computed(() => {
      return ALLOWED_FILE_TYPES.map(type => `.${type}`).join(',')
    })

    const isFormValid = computed(() => {
      return form.value.titulo &&
             form.value.tipo &&
             (form.value.archivo || existingFile.value) &&
             Object.keys(errors.value).length === 0
    })

    const selectedEntityInfo = computed(() => {
      if (form.value.tipoRelacion === 'propiedad' && form.value.propiedadData.id) {
        return `${form.value.propiedadData.tipo === 'trastero' ? 'Trastero' : 'Piso'} ID: ${form.value.propiedadData.id}`
      }
      if (form.value.tipoRelacion === 'cliente' && form.value.client_id) {
        return `Cliente ID: ${form.value.client_id}`
      }
      if (form.value.tipoRelacion === 'alquiler' && form.value.alquiler_id) {
        const alquiler = alquileres.value.find(a => a.id == form.value.alquiler_id)
        return alquiler ? `Contrato: ${alquiler.cliente?.nombre} - ${formatAlquilerOption(alquiler)}` : `Contrato ID: ${form.value.alquiler_id}`
      }
      return null
    })

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const getFileName = () => {
      if (form.value.archivo) return form.value.archivo.name
      if (existingFile.value) return existingFile.value.nombre_original
      return ''
    }

    const getFileSize = () => {
      if (form.value.archivo) return formatFileSize(form.value.archivo.size)
      if (existingFile.value) return formatFileSize(existingFile.value.tamano)
      return ''
    }

    const getFileIcon = () => {
      const fileName = getFileName()
      const ext = fileName.split('.').pop()?.toLowerCase()

      const iconMap = {
        pdf: 'fas fa-file-pdf text-danger',
        doc: 'fas fa-file-word text-primary',
        docx: 'fas fa-file-word text-primary',
        xls: 'fas fa-file-excel text-success',
        xlsx: 'fas fa-file-excel text-success',
        jpg: 'fas fa-file-image text-info',
        jpeg: 'fas fa-file-image text-info',
        png: 'fas fa-file-image text-info'
      }

      return iconMap[ext] || 'fas fa-file text-secondary'
    }

    const isImageFile = () => {
      const fileName = getFileName()
      const ext = fileName.split('.').pop()?.toLowerCase()
      return ['jpg', 'jpeg', 'png'].includes(ext)
    }

    const getFileTypeDescription = () => {
      const fileName = getFileName()
      const ext = fileName.split('.').pop()?.toLowerCase()

      const descriptions = {
        pdf: 'Documento PDF',
        doc: 'Documento Word',
        docx: 'Documento Word',
        xls: 'Hoja de cálculo Excel',
        xlsx: 'Hoja de cálculo Excel',
        jpg: 'Imagen JPEG',
        jpeg: 'Imagen JPEG',
        png: 'Imagen PNG'
      }

      return descriptions[ext] || 'Archivo'
    }

    const validateFile = (file) => {
      // Validar tamaño
      if (file.size > MAX_FILE_SIZE) {
        errors.value.archivo = `El archivo es demasiado grande. Tamaño máximo: ${formatFileSize(MAX_FILE_SIZE)}`
        return false
      }

      // Validar tipo
      const fileExt = file.name.split('.').pop().toLowerCase()
      if (!ALLOWED_FILE_TYPES.includes(fileExt)) {
        errors.value.archivo = `Tipo de archivo no permitido. Formatos permitidos: ${ALLOWED_FILE_TYPES.join(', ').toUpperCase()}`
        return false
      }

      errors.value.archivo = null
      return true
    }

    const handleFileChange = (event) => {
      const file = event.target.files[0]
      if (!file) return

      if (validateFile(file)) {
        form.value.archivo = file
        createFilePreview(file)
      } else {
        event.target.value = ''
      }
    }

    const handleFileDrop = (event) => {
      isDragOver.value = false
      const file = event.dataTransfer.files[0]

      if (!file) return

      if (validateFile(file)) {
        form.value.archivo = file
        createFilePreview(file)
      }
    }

    const createFilePreview = (file) => {
      if (isImageFile() && file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          filePreviewUrl.value = e.target.result
        }
        reader.readAsDataURL(file)
      }
    }

    const removeFile = () => {
      form.value.archivo = null
      filePreviewUrl.value = null
      existingFile.value = null
      errors.value.archivo = null
    }

    const handleTipoRelacionChange = () => {
      // Limpiar datos de relación anterior
      form.value.propiedadData = { tipo: '', id: null }
      form.value.client_id = null
      form.value.alquiler_id = null

      // Cargar alquileres si es necesario
      if (form.value.tipoRelacion === 'alquiler') {
        loadAlquileres()
      }
    }

    const handlePropiedadChange = (propiedadData) => {
      form.value.propiedadData = propiedadData
    }

    const handleClienteChange = (cliente) => {
      // El cliente ya se asigna automáticamente por el v-model
    }

    const loadAlquileres = async () => {
      isLoadingAlquileres.value = true
      try {
        const response = await alquileresService.getAll()
        alquileres.value = response.data
      } catch (error) {
        console.error('Error al cargar alquileres:', error)
        toast.error('Error al cargar la lista de contratos')
      } finally {
        isLoadingAlquileres.value = false
      }
    }

    const formatAlquilerOption = (alquiler) => {
      const cliente = alquiler.cliente ? `${alquiler.cliente.nombre} ${alquiler.cliente.apellidos}` : 'Cliente desconocido'
      const propiedad = alquiler.propiedad_tipo === 'App\\Trastero' ? 'Trastero' : 'Piso'
      return `${cliente} - ${propiedad} (${alquiler.fecha_inicio})`
    }

    const validateForm = () => {
      errors.value = {}

      if (!form.value.titulo?.trim()) {
        errors.value.titulo = 'El título es obligatorio'
      }

      if (!form.value.tipo) {
        errors.value.tipo = 'Debe seleccionar el tipo de documento'
      }

      if (!form.value.archivo && !existingFile.value) {
        errors.value.archivo = 'Debe seleccionar un archivo'
      }

      if (form.value.fecha_vencimiento && form.value.fecha_documento &&
          moment(form.value.fecha_vencimiento).isBefore(moment(form.value.fecha_documento))) {
        errors.value.fecha_vencimiento = 'La fecha de vencimiento debe ser posterior a la fecha del documento'
      }

      return Object.keys(errors.value).length === 0
    }

    const saveDraft = async () => {
      const originalEstado = form.value.estado
      form.value.estado = 'borrador'

      const success = await handleSubmit()

      if (!success) {
        form.value.estado = originalEstado
      }
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return false
      }

      isSubmitting.value = true
      uploadProgress.value = 0

      try {
        const formData = new FormData()
        formData.append('titulo', form.value.titulo)
        formData.append('tipo', form.value.tipo)
        formData.append('descripcion', form.value.descripcion || '')
        formData.append('fecha_documento', form.value.fecha_documento || '')
        formData.append('fecha_vencimiento', form.value.fecha_vencimiento || '')
        formData.append('estado', form.value.estado)
        formData.append('es_confidencial', form.value.es_confidencial ? '1' : '0')

        // Añadir datos de relación según el tipo
        if (form.value.tipoRelacion === 'propiedad' && form.value.propiedadData.id) {
          formData.append('tipo_propiedad', form.value.propiedadData.tipo === 'trastero' ? 'App\\Trastero' : 'App\\Piso')
          formData.append('propiedad_id', form.value.propiedadData.id)
        } else if (form.value.tipoRelacion === 'cliente' && form.value.client_id) {
          formData.append('client_id', form.value.client_id)
        } else if (form.value.tipoRelacion === 'alquiler' && form.value.alquiler_id) {
          formData.append('alquiler_id', form.value.alquiler_id)
        }

        if (form.value.archivo) {
          formData.append('archivo', form.value.archivo)
        }

        if (isEdit.value) {
          await documentosService.update(props.id, formData)
          toast.success('Documento actualizado correctamente')
        } else {
          await documentosService.upload(formData)
          toast.success('Documento subido correctamente')
        }

        router.push('/documentos')
        return true
      } catch (error) {
        console.error('Error al guardar documento:', error)
        const message = error.response?.data?.message || 'Error al guardar el documento'
        toast.error(message)

        if (error.response?.data?.errors) {
          errors.value = error.response.data.errors
        }

        return false
      } finally {
        isSubmitting.value = false
        uploadProgress.value = 0
      }
    }

    // Cargar datos si es edición
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await documentosService.getById(props.id)
          const documento = response.data

          form.value = {
            titulo: documento.titulo || '',
            tipo: documento.tipo || '',
            descripcion: documento.descripcion || '',
            fecha_documento: documento.fecha_documento || null,
            fecha_vencimiento: documento.fecha_vencimiento || null,
            tipoRelacion: documento.tipo_propiedad ? 'propiedad' : (documento.client_id ? 'cliente' : (documento.alquiler_id ? 'alquiler' : '')),
            propiedadData: documento.tipo_propiedad ? {
              tipo: documento.tipo_propiedad === 'App\\Trastero' ? 'trastero' : 'piso',
              id: documento.propiedad_id
            } : { tipo: '', id: null },
            client_id: documento.client_id || null,
            alquiler_id: documento.alquiler_id || null,
            estado: documento.estado || 'pendiente',
            es_confidencial: documento.es_confidencial || false,
            archivo: null
          }

          if (documento.archivo) {
            existingFile.value = documento.archivo
          }

        } catch (error) {
          console.error('Error al cargar documento:', error)
          toast.error('Error al cargar los datos del documento')
          router.push('/documentos')
        }
      }
    })

    return {
      form,
      errors,
      today,
      isEdit,
      isSubmitting,
      isDragOver,
      uploadProgress,
      alquileres,
      isLoadingAlquileres,
      filePreviewUrl,
      existingFile,
      acceptedFileTypes,
      isFormValid,
      selectedEntityInfo,
      MAX_FILE_SIZE,
      ALLOWED_FILE_TYPES,
      formatFileSize,
      getFileName,
      getFileSize,
      getFileIcon,
      isImageFile,
      getFileTypeDescription,
      handleFileChange,
      handleFileDrop,
      removeFile,
      handleTipoRelacionChange,
      handlePropiedadChange,
      handleClienteChange,
      formatAlquilerOption,
      saveDraft,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.documento-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.documento-form .card-title {
  color: #495057;
  font-weight: 600;
}

.file-drop-zone {
  border: 2px dashed #dee2e6;
  border-radius: 0.375rem;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.file-drop-zone:hover {
  border-color: #86b7fe;
  background-color: #f0f8ff;
}

.file-drop-zone.drag-over {
  border-color: #0d6efd;
  background-color: #e7f3ff;
  transform: scale(1.02);
}

.file-drop-zone.has-file {
  border-color: #198754;
  background-color: #f8fff9;
  text-align: left;
}

.file-preview {
  padding: 0.5rem;
}

.file-preview img {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.progress {
  height: 0.5rem;
  border-radius: 0.25rem;
}

.progress-bar {
  background-color: #0d6efd;
  transition: width 0.3s ease;
}

.documento-form .form-check {
  padding-left: 1.5rem;
}

.documento-form .form-check-input {
  margin-left: -1.5rem;
}

.documento-form .form-check-label {
  font-weight: 500;
  color: #495057;
}

.documento-form .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.documento-form .gap-2 {
  gap: 0.5rem !important;
}

.documento-form .form-control:focus,
.documento-form .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.documento-form .is-invalid {
  border-color: #dc3545;
}

.documento-form .invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.documento-form .form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.documento-form .alert {
  border: none;
  border-radius: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .documento-form .card-body {
    padding: 1rem;
  }

  .documento-form .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .documento-form .d-flex {
    flex-direction: column;
  }

  .file-drop-zone {
    padding: 0.75rem;
  }
}

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.file-preview {
  animation: fadeIn 0.3s ease;
}
</style>
import api from './api'

export const pisosService = {
  // Obtener todos los pisos
  getAll(params = {}) {
    return api.get('/pisos',
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    );
  },

  // Obtener piso por ID
  getById(id) {
    return api.get(`/pisos/ver/${id}`,
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    );
  },

  // Crear nuevo piso
  create(data) {
    return api.post('/pisos/create', 
      { data },
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    )
  },

  // Actualizar piso
  update(id, data) {
    return api.post(`/pisos/update/${id}`, 
      { data },
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    )
  },

  // Eliminar piso
  delete(id) {
    return api.get(`/pisos/${id}`,
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    );
  },

  // Obtener pisos disponibles
  getDisponibles() {
    return api.get('/pisos-disponibles')
  },

  // Obtener pisos ocupados
  getOcupados() {
    return api.get('/pisos-ocupados')
  }
}

<template>
  <div class="piso-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Añadir' }} Piso</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del piso' : 'Registra un nuevo piso en el sistema' }}</p>
        </div>
      </div>

      <form @submit.prevent="handleSubmit">
        <!-- Información básica -->
        <div class="row">
          <div class="col-lg-8">
            <div class="card mb-4" :class="{ 'has-error': hasSectionErrors('info-basica') }">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-home me-2"></i>Información Básica
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label required">Número de Piso</label>
                    <input
                      v-model="form.numero_piso"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.numero_piso }"
                      placeholder="Ej: A-101"
                      required
                    >
                    <div v-if="errors.numero_piso" class="invalid-feedback">
                      {{ errors.numero_piso }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label required">Tipo de Piso</label>
                    <select
                      v-model="form.tipo_piso"
                      class="form-select"
                      :class="{ 'is-invalid': errors.tipo_piso }"
                      required
                    >
                      <option value="">Selecciona...</option>
                      <option value="estudio">Estudio</option>
                      <option value="apartamento">Apartamento</option>
                      <option value="piso">Piso</option>
                      <option value="atico">Ático</option>
                      <option value="duplex">Dúplex</option>
                    </select>
                    <div v-if="errors.tipo_piso" class="invalid-feedback">
                      {{ errors.tipo_piso }}
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-8">
                    <label class="form-label required">Dirección</label>
                    <input
                      v-model="form.direccion"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.direccion }"
                      placeholder="Ej: Calle Mayor, 123"
                      required
                    >
                    <div v-if="errors.direccion" class="invalid-feedback">
                      {{ errors.direccion }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label required">Piso/Puerta</label>
                    <input
                      v-model="form.piso"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.piso }"
                      placeholder="Ej: 2º A, Bajo"
                      required
                    >
                    <div v-if="errors.piso" class="invalid-feedback">
                      {{ errors.piso }}
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-12">
                    <label class="form-label">Dirección adicional</label>
                    <input
                      v-model="form.direccion2"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.direccion2 }"
                      placeholder="Ej: Urbanización, Bloque, etc."
                    >
                    <div v-if="errors.direccion2" class="invalid-feedback">
                      {{ errors.direccion2 }}
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-4">
                    <label class="form-label required">Código postal</label>
                    <input
                      v-model="form.cp"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.cp }"
                      placeholder="08001"
                      maxlength="5"
                      required
                    >
                    <div v-if="errors.cp" class="invalid-feedback">
                      {{ errors.cp }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label required">Ciudad</label>
                    <input
                      v-model="form.ciudad"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.ciudad }"
                      placeholder="Barcelona"
                      required
                    >
                    <div v-if="errors.ciudad" class="invalid-feedback">
                      {{ errors.ciudad }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Provincia</label>
                    <input
                      v-model="form.provincia"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.provincia }"
                      placeholder="Barcelona"
                    >
                    <div v-if="errors.provincia" class="invalid-feedback">
                      {{ errors.provincia }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Estado y disponibilidad -->
          <div class="col-lg-4">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-info-circle me-2"></i>Estado
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label required">Disponibilidad</label>
                  <div class="form-check form-switch">
                    <input
                      v-model="form.disponible"
                      class="form-check-input"
                      type="checkbox"
                      id="disponible"
                    >
                    <label class="form-check-label" for="disponible">
                      {{ form.disponible ? 'Disponible' : 'No disponible' }}
                    </label>
                  </div>
                </div>

                <div class="mb-3">
                  <ClienteSelector
                    v-model="form.client_id"
                    label="Cliente asignado"
                    :error="errors.client_id"
                    help-text="Cliente asociado a este piso (opcional)"
                    @change="handleClienteChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Características físicas -->
        <div class="row">
          <div class="col-12">
            <div class="card mb-4" :class="{ 'has-error': hasSectionErrors('caracteristicas') }">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-ruler-combined me-2"></i>Características Físicas
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-4">
                    <label class="form-label required">Superficie (m²)</label>
                    <input
                      v-model="form.metros_cuadrados"
                      type="number"
                      step="0.01"
                      min="1"
                      class="form-control"
                      :class="{ 'is-invalid': errors.metros_cuadrados }"
                      placeholder="75.50"
                      required
                    >
                    <div v-if="errors.metros_cuadrados" class="invalid-feedback">
                      {{ errors.metros_cuadrados }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label required">Habitaciones</label>
                    <select
                      v-model="form.habitaciones"
                      class="form-select"
                      :class="{ 'is-invalid': errors.habitaciones }"
                      required
                    >
                      <option value="">Selecciona...</option>
                      <option value="1">1 habitación</option>
                      <option value="2">2 habitaciones</option>
                      <option value="3">3 habitaciones</option>
                      <option value="4">4 habitaciones</option>
                      <option value="5">5+ habitaciones</option>
                    </select>
                    <div v-if="errors.habitaciones" class="invalid-feedback">
                      {{ errors.habitaciones }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label required">Baños</label>
                    <select
                      v-model="form.banos"
                      class="form-select"
                      :class="{ 'is-invalid': errors.banos }"
                      required
                    >
                      <option value="">Selecciona...</option>
                      <option value="1">1 baño</option>
                      <option value="2">2 baños</option>
                      <option value="3">3 baños</option>
                      <option value="4">4+ baños</option>
                    </select>
                    <div v-if="errors.banos" class="invalid-feedback">
                      {{ errors.banos }}
                    </div>
                  </div>
                </div>

                <!-- Características adicionales -->
                <div class="row mb-3">
                  <div class="col-md-12">
                    <label class="form-label">Características adicionales</label>
                    <div class="row">
                      <div class="col-md-3">
                        <div class="form-check">
                          <input
                            v-model="caracteristicasObj.ascensor"
                            class="form-check-input"
                            type="checkbox"
                            id="ascensor"
                          >
                          <label class="form-check-label" for="ascensor">
                            Ascensor
                          </label>
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="form-check">
                          <input
                            v-model="caracteristicasObj.terraza"
                            class="form-check-input"
                            type="checkbox"
                            id="terraza"
                          >
                          <label class="form-check-label" for="terraza">
                            Terraza/Balcón
                          </label>
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="form-check">
                          <input
                            v-model="caracteristicasObj.parking"
                            class="form-check-input"
                            type="checkbox"
                            id="parking"
                          >
                          <label class="form-check-label" for="parking">
                            Parking
                          </label>
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="form-check">
                          <input
                            v-model="form.amueblado"
                            class="form-check-input"
                            type="checkbox"
                            id="amueblado"
                          >
                          <label class="form-check-label" for="amueblado">
                            Amueblado
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Precio y condiciones económicas -->
        <div class="row">
          <div class="col-lg-6">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-euro-sign me-2"></i>Condiciones Económicas
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <PriceInput
                    v-model="form.precio_mensual"
                    label="Precio mensual *"
                    :error="errors.precio_mensual"
                    show-per-month
                    :show-calculations="true"
                    help-text="Precio mensual del alquiler"
                    required
                  />
                </div>

                <div class="mb-3">
                  <PriceInput
                    v-model="form.fianza"
                    label="Fianza"
                    :error="errors.fianza"
                    help-text="Normalmente 1-2 meses"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Información del propietario -->
          <div class="col-lg-6">
            <div class="card mb-4" :class="{ 'has-error': hasSectionErrors('propietario') }">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-user me-2"></i>Información del Propietario
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Nombre del propietario</label>
                  <input
                    v-model="form.propietario_nombre"
                    type="text"
                    class="form-control"
                    :class="{ 'is-invalid': errors.propietario_nombre }"
                    placeholder="Nombre completo"
                  >
                  <div v-if="errors.propietario_nombre" class="invalid-feedback">
                    {{ errors.propietario_nombre }}
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <label class="form-label">Teléfono del propietario</label>
                    <input
                      v-model="form.propietario_telefono"
                      type="tel"
                      class="form-control"
                      :class="{ 'is-invalid': errors.propietario_telefono }"
                      placeholder="Teléfono de contacto"
                    >
                    <div v-if="errors.propietario_telefono" class="invalid-feedback">
                      {{ errors.propietario_telefono }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Email del propietario</label>
                    <input
                      v-model="form.propietario_email"
                      type="email"
                      class="form-control"
                      :class="{ 'is-invalid': errors.propietario_email }"
                      placeholder="Email de contacto"
                    >
                    <div v-if="errors.propietario_email" class="invalid-feedback">
                      {{ errors.propietario_email }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tarjeta de descripción -->
        <div class="row">
          <div class="col-12">
            <div class="card mb-4" :class="{ 'has-error': hasSectionErrors('descripcion') }">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-clipboard-list me-2"></i>Descripción
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label required">Descripción detallada</label>
                  <textarea
                    v-model="form.descripcion"
                    class="form-control"
                    :class="{ 'is-invalid': errors.descripcion }"
                    rows="4"
                    placeholder="Describe las características especiales, equipamiento, reformas recientes, etc."
                    required
                  ></textarea>
                  <div v-if="errors.descripcion" class="invalid-feedback">
                    {{ errors.descripcion }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Botones de acción -->
        <div class="row">
          <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
              <router-link to="/pisos" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>Cancelar
              </router-link>
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="isSubmitting || !isFormValid"
              >
                <i class="fas fa-check me-1"></i>
                {{ isSubmitting ? 'Guardando...' : (isEdit ? 'Actualizar' : 'Crear piso') }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { pisosService } from '@/services/pisos'
import PriceInput from '@/components/common/PriceInput.vue'
import ClienteSelector from '@/components/common/ClienteSelector.vue'

export default {
  name: 'PisoForm',
  components: {
    PriceInput,
    ClienteSelector
  },
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()

    const isSubmitting = ref(false)
    const clientID = ref(false)
    const errors = ref({})

    //const selectedPiso = (!!props.id) ? null :props.id;

    // Objeto para manejar las características como checkboxes
    const caracteristicasObj = ref({
      ascensor: false,
      terraza: false,
      parking: false,
      aire_acondicionado: false,
      calefaccion: false,
      piscina: false,
      jardin: false,
      seguridad: false
    })

    const form = ref({
      numero_piso: '',
      direccion: '',
      direccion2: '',
      piso: '',
      cp: '',
      ciudad: 'Barcelona',
      provincia: 'Barcelona',
      habitaciones: '',
      banos: '',
      metros_cuadrados: null,
      descripcion: '',
      precio_mensual: null,
      fianza: null,
      amueblado: false,
      disponible: true,
      tipo_piso: '',
      caracteristicas: null, // Se llenará con JSON.stringify(caracteristicasObj)
      propietario_nombre: '',
      propietario_telefono: '',
      propietario_email: '',
      client_id: null
    })

    // Sincronizar caracteristicasObj con form.caracteristicas
    watch(caracteristicasObj, (newVal) => {
      form.value.caracteristicas = JSON.stringify(newVal)
    }, { deep: true })

    const isEdit = computed(() => !!props.id)

    const isFormValid = computed(() => {
      return form.value.numero_piso &&
             form.value.direccion &&
             form.value.piso &&
             form.value.cp &&
             form.value.ciudad &&
             form.value.tipo_piso &&
             form.value.habitaciones &&
             form.value.banos &&
             form.value.metros_cuadrados &&
             form.value.precio_mensual &&
             form.value.descripcion && // Añadimos la descripción como campo obligatorio
             Object.keys(errors.value).length === 0
    })

    const validateForm = () => {
      errors.value = {};

      // Validaciones básicas
      if (!form.value.numero_piso?.trim()) {
        errors.value.numero_piso = 'El número de piso es obligatorio';
      }

      if (!form.value.direccion?.trim()) {
        errors.value.direccion = 'La dirección es obligatoria'
      }

      if (!form.value.piso?.trim()) {
        errors.value.piso = 'El piso/puerta es obligatorio'
      }

      if (!form.value.cp?.trim()) {
        errors.value.cp = 'El código postal es obligatorio'
      } else if (!/^\d{5}$/.test(form.value.cp)) {
        errors.value.cp = 'El código postal debe tener 5 dígitos'
      }

      if (!form.value.ciudad?.trim()) {
        errors.value.ciudad = 'La ciudad es obligatoria'
      }

      if (!form.value.tipo_piso) {
        errors.value.tipo_piso = 'Debe seleccionar el tipo de piso'
      }

      if (!form.value.habitaciones) {
        errors.value.habitaciones = 'Debe seleccionar el número de habitaciones'
      }

      if (!form.value.banos) {
        errors.value.banos = 'Debe seleccionar el número de baños'
      }

      if (!form.value.metros_cuadrados || form.value.metros_cuadrados <= 0) {
        errors.value.metros_cuadrados = 'La superficie debe ser mayor a 0'
      }

      if (!form.value.precio_mensual || form.value.precio_mensual <= 0) {
        errors.value.precio_mensual = 'El precio mensual debe ser mayor a 0'
      }

      // Validar email del propietario si se proporciona
      if (form.value.propietario_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.propietario_email)) {
        errors.value.propietario_email = 'El email no es válido'
      }

      // Validación para la descripción
      if (!form.value.descripcion?.trim()) {
        errors.value.descripcion = 'La descripción es obligatoria';
      }

      // Si hay errores, hacer scroll al primer campo con error
      if (Object.keys(errors.value).length > 0) {
        nextTick(() => {
          const firstErrorElement = document.querySelector('.is-invalid');
          if (firstErrorElement) {
            firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstErrorElement.focus();
          }
        });
      }

      return Object.keys(errors.value).length === 0;
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return false
      }

      isSubmitting.value = true

      try {
        // Preparar datos para envío
        const submitData = {
          ...form.value,
          habitaciones: parseInt(form.value.habitaciones),
          banos: parseInt(form.value.banos),
          metros_cuadrados: parseFloat(form.value.metros_cuadrados),
          precio_mensual: parseFloat(form.value.precio_mensual),
          fianza: form.value.fianza ? parseFloat(form.value.fianza) : null,
          caracteristicas: JSON.stringify(caracteristicasObj.value),
          client_id: form.value.client_id
        }

        if (isEdit.value) {
          await pisosService.update(props.id, submitData)
          toast.success('Piso actualizado correctamente')
        } else {
          await pisosService.create(submitData)
          toast.success('Piso creado correctamente')
        }

        router.push('/pisos')
        return true
      } catch (error) {
        console.error('Error al guardar piso:', error)
        const message = error.response?.data?.message || 'Error al guardar el piso'
        toast.error(message)

        // Manejar errores de validación del servidor
        if (error.response?.data?.errors) {
          errors.value = error.response.data.errors
        }

        return false
      } finally {
        isSubmitting.value = false
      }
    }

    // Cargar datos si es edición
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await pisosService.getById(props.id)
          const piso = response.data.piso

          // Mapear los datos del servidor al formulario
          form.value = {
            numero_piso: piso.numero_piso || '',
            direccion: piso.direccion || '',
            direccion2: piso.direccion2 || '',
            piso: piso.piso || '',
            cp: piso.cp || '',
            ciudad: piso.ciudad || 'Barcelona',
            provincia: piso.provincia || 'Barcelona',
            habitaciones: piso.habitaciones?.toString() || '',
            banos: piso.banos?.toString() || '',
            metros_cuadrados: piso.metros_cuadrados || null,
            descripcion: piso.descripcion || '',
            precio_mensual: piso.precio_mensual || null,
            fianza: piso.fianza || null,
            amueblado: piso.amueblado || false,
            disponible: piso.disponible || true,
            tipo_piso: piso.tipo_piso || '',
            propietario_nombre: piso.propietario_nombre || '',
            propietario_telefono: piso.propietario_telefono || '',
            propietario_email: piso.propietario_email || '',
            client_id: piso.client_id || null
          }

          // Cargar características si existen
          if (piso.caracteristicas) {
            try {
              const caracteristicas = JSON.parse(piso.caracteristicas)
              caracteristicasObj.value = {
                ...caracteristicasObj.value,
                ...caracteristicas
              }
            } catch (e) {
              console.error('Error al parsear características:', e)
            }
          }
        } catch (error) {
          console.error('Error al cargar piso:', error)
          toast.error('Error al cargar los datos del piso')
          router.push('/pisos')
        }
      }
    })

    const handleClienteChange = (cliente) => {
      form.value.client_id = cliente ? cliente.id : null;
    }

    const hasSectionErrors = (section) => {
      const sectionFields = {
        'info-basica': ['numero_piso', 'direccion', 'piso', 'cp', 'ciudad', 'tipo_piso'],
        'caracteristicas': ['habitaciones', 'banos', 'metros_cuadrados', 'precio_mensual'],
        'propietario': ['propietario_email'],
        'descripcion': ['descripcion'] // Añadimos la sección de descripción
      };
      
      return sectionFields[section]?.some(field => errors.value[field]) || false;
    };

    return {
      form,
      errors,
      isEdit,
      isSubmitting,
      isFormValid,
      caracteristicasObj,
      handleSubmit,
      handleClienteChange,
      hasSectionErrors
    }
  }
}
</script>

<style scoped>
.piso-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.piso-form .card-title {
  color: #495057;
  font-weight: 600;
}

.piso-form .form-check {
  padding-left: 1.5rem;
}

.piso-form .form-check-input {
  margin-left: -1.5rem;
}

.piso-form .form-check-label {
  font-weight: 500;
  color: #495057;
}

.piso-form .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.piso-form .gap-2 {
  gap: 0.5rem !important;
}

.piso-form .form-control:focus,
.piso-form .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.piso-form .is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.15rem rgba(220, 53, 69, 0.25);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  padding-right: calc(1.5em + 0.75rem);
}

.piso-form .is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.piso-form .invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
  font-weight: 500;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Añadir un asterisco rojo a las etiquetas de campos obligatorios */
.piso-form .form-label.required::after {
  content: " *";
  color: #dc3545;
  font-weight: bold;
}

/* Estilo para el borde de la tarjeta cuando contiene campos inválidos */
.piso-form .card.has-error {
  border-left: 3px solid #dc3545;
}

.piso-form .form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.piso-form .alert {
  border: none;
  border-radius: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .piso-form .card-body {
    padding: 1rem;
  }

  .piso-form .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .piso-form .d-flex {
    flex-direction: column;
  }
}
</style>


















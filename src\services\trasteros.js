import api from './api'

export const trasterosService = {

  
  // Obtener todos los trasteros
  getAll(params = {}) {
    return api.get('/trasteros',
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    );
  },

  // Obtener trastero por ID
  getById(id) {
    return api.get(`/trasteros/ver/${id}`,
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    ) 
  },

  // Crear nuevo trastero
  create(data) {
    return api.post('/trasteros/create'),
      { data },
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
  },

  // Actualizar trastero
  update(id, data) {
    return api.post(`/trasteros/update/${id}`,
      { data },
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      })
      
  },

  // Eliminar trastero
  delete(id) {
    return api.get(`/trasteros/delete/${id}`,
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    ) 
  },

  // Obtener trasteros disponibles
  getDisponibles() {
    return api.get('/trasteros-disponibles')
  },

  // Obtener trasteros ocupados
  getOcupados() {
    return api.get('/trasteros-ocupados')
  },

  // Obtener trasteros por tipo
  getPorTipo(tipoId) {
    return api.get(`/trasteros-tipo/${tipoId}`)
  }
}
